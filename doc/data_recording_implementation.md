# 数据记录实现文档

## 概述

本文档描述了间歇流应用的实时数据记录实现，确保在每个关键节点都记录数据，防止数据丢失。

## 记录频率和时机

### 之前的记录方式 ❌
- **只在会话完成时记录一次**
- **数据丢失风险高**：应用崩溃或被强制关闭时，正在进行的会话数据会丢失
- **无法追踪过程数据**：缺少会话进行过程中的详细记录

### 现在的记录方式 ✅
- **关键节点实时记录**：每个重要时刻都立即保存到数据库
- **定期进度保存**：每30秒自动保存会话进度
- **数据安全性高**：即使应用异常退出，数据也不会丢失

## 具体记录时机

### 1. 会话开始 📝
**时机**：用户点击开始专注，会话状态变为`active`时
**记录内容**：
- 会话基本信息（ID、开始时间、持续时间等）
- 会话状态
- 初始进度数据

**代码位置**：`FocusScreen._startFocusSession()`
```dart
if (widget.session.state == FocusSessionState.notStarted) {
  widget.session.start();
  // 记录会话开始
  _sessionDataManager.startSession(widget.session);
}
```

### 2. 小休开始 📝
**时机**：触发小休息，显示休息界面时
**记录内容**：
- 新的休息记录（开始时间、预计持续时间）
- 更新会话的休息列表
- 当前会话进度

**代码位置**：`FocusScreen._triggerBreak()`
```dart
widget.session.addBreak(breakRecord);
// 记录小休开始
_sessionDataManager.recordBreakStart(breakRecord);
```

### 3. 小休结束 📝
**时机**：小休息倒计时结束，恢复专注时
**记录内容**：
- 更新休息记录状态
- 当前会话进度
- 会话状态变更

**代码位置**：`FocusScreen._endBreak()`
```dart
// 记录小休结束
_sessionDataManager.recordBreakEnd();
```

### 4. 会话暂停 📝
**时机**：用户手动暂停专注会话时
**记录内容**：
- 会话状态变更为`paused`
- 当前进度数据
- 暂停时间点

**代码位置**：`FocusScreen._pauseSession()`
```dart
widget.session.pause();
// 记录会话暂停
_sessionDataManager.recordSessionPause();
```

### 5. 会话恢复 📝
**时机**：用户从暂停状态恢复专注时
**记录内容**：
- 会话状态变更为`active`
- 恢复时间点
- 当前进度数据

**代码位置**：`FocusScreen._resumeSession()`
```dart
widget.session.resume();
// 记录会话恢复
_sessionDataManager.recordSessionResume();
```

### 6. 会话完成 📝
**时机**：90分钟专注时间结束时
**记录内容**：
- 会话状态变更为`completed`
- 结束时间
- 最终统计数据
- 触发成就检查

**代码位置**：`FocusScreen._completeSession()`
```dart
widget.session.complete();
// 记录会话完成
_sessionDataManager.recordSessionComplete();
```

### 7. 会话取消 📝
**时机**：用户主动停止或取消专注会话时
**记录内容**：
- 会话状态变更为`cancelled`
- 取消时间
- 当前进度数据

**代码位置**：`FocusScreen._stopSession()`
```dart
widget.session.cancel();
// 记录会话取消
_sessionDataManager.recordSessionCancel();
```

### 8. 定期进度保存 📝
**时机**：每30秒自动执行
**记录内容**：
- 当前已用时间
- 会话状态
- 休息记录更新

**代码位置**：`FocusScreen._startProgressSaveTimer()`
```dart
_progressSaveTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
  // 定期保存会话进度
  _sessionDataManager.recordProgressUpdate();
});
```

## 技术实现

### SessionDataManager
**职责**：统一管理所有数据记录操作
**特点**：
- 单例模式，全局唯一实例
- 异步操作，不阻塞UI
- 错误处理，记录失败不影响应用运行
- 自动成就检查和更新

### 数据流程
```
UI事件 → SessionDataManager → FocusRepository → FocusSessionDao → SQLite数据库
```

### 错误处理
- **记录失败不影响用户体验**：所有数据库操作都有try-catch包装
- **日志记录**：记录操作结果，便于调试
- **回退机制**：即使数据库操作失败，应用仍能正常运行

## 数据安全性

### 防止数据丢失
1. **实时记录**：关键操作立即保存，不等待会话结束
2. **定期备份**：每30秒自动保存进度
3. **事务保护**：使用数据库事务确保数据一致性
4. **异常恢复**：应用重启后可以恢复未完成的会话

### 性能优化
1. **异步操作**：所有数据库操作都是异步的，不阻塞UI
2. **批量操作**：相关数据在同一事务中处理
3. **索引优化**：为常用查询字段创建索引
4. **缓存机制**：复杂统计查询结果可以缓存

## 使用示例

### 开始新会话
```dart
final session = FocusSession(
  id: DateTime.now().millisecondsSinceEpoch.toString(),
  startTime: DateTime.now(),
  durationMinutes: 90,
);

// 会话开始时自动记录
await SessionDataManager.instance.startSession(session);
```

### 查看记录的数据
```dart
// 获取今日所有会话
final todaySessions = await FocusRepository.instance.getTodaySessions();

// 获取统计数据
final statistics = await StatisticsRepository.instance.getStatisticsData();
```

## 监控和调试

### 日志输出
每个记录操作都会输出日志，便于调试：
```
SessionDataManager: Starting session 1703123456789
SessionDataManager: Session start recorded successfully
SessionDataManager: Recording break start at 2023-12-21 10:30:00
SessionDataManager: Break start recorded successfully
```

### 数据库查询
可以直接查询数据库验证记录是否正确：
```dart
final info = await DatabaseService.instance.getDatabaseInfo();
print('会话记录数: ${info['focus_sessions_count']}');
print('休息记录数: ${info['break_records_count']}');
```

## 总结

新的数据记录实现确保了：

1. ✅ **数据完整性**：每个关键节点都有记录
2. ✅ **数据安全性**：实时保存，防止丢失
3. ✅ **用户体验**：异步操作，不影响UI流畅性
4. ✅ **可维护性**：清晰的架构和错误处理
5. ✅ **可扩展性**：易于添加新的记录点

这种实现方式完全满足了用户的要求："每一次小休息和小休结束等这些关键节点都记录一次"，并且提供了更好的数据安全性和用户体验。
