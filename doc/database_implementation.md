# 数据库实现文档

## 概述

本文档描述了间歇流应用的SQLite数据库实现，包括数据库架构、数据访问层和使用方法。

## 架构设计

### 分层架构

```
UI层 (Screens/Widgets)
    ↓
服务层 (DatabaseService)
    ↓
仓库层 (FocusRepository, StatisticsRepository)
    ↓
数据访问层 (DAO)
    ↓
数据库层 (DatabaseHelper, Tables, Migrations)
    ↓
SQLite数据库
```

### 核心组件

1. **DatabaseHelper**: 数据库管理和连接
2. **DatabaseTables**: 表结构定义
3. **DatabaseMigrations**: 数据库版本迁移
4. **DAO层**: 数据访问对象
5. **Repository层**: 业务逻辑封装
6. **DatabaseService**: 统一服务接口

## 数据库表结构

### 1. focus_sessions (专注会话表)
- `id`: 会话唯一标识
- `start_time`: 开始时间
- `end_time`: 结束时间
- `duration_minutes`: 持续时间(分钟)
- `state`: 会话状态
- `elapsed_minutes`: 已用时间(分钟)
- `elapsed_seconds`: 已用时间(秒)
- `is_completed`: 是否完成
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 2. break_records (休息记录表)
- `id`: 记录ID
- `session_id`: 关联会话ID
- `start_time`: 休息开始时间
- `duration_seconds`: 休息时长(秒)
- `reaction_time_seconds`: 反应时间(秒)
- `created_at`: 创建时间

### 3. achievements (成就表)
- `id`: 成就ID
- `name`: 成就名称
- `description`: 成就描述
- `type`: 成就类型
- `unlocked_at`: 解锁时间
- `created_at`: 创建时间

### 4. app_settings (应用设置表)
- 存储所有应用设置参数
- 单行记录，ID固定为1

### 5. statistics_cache (统计缓存表)
- 用于缓存复杂统计查询结果
- 提高查询性能

## 使用方法

### 1. 初始化数据库

```dart
// 在应用启动时初始化
await DatabaseService.instance.initialize();
```

### 2. 保存专注会话

```dart
final session = FocusSession(
  id: 'session_123',
  startTime: DateTime.now(),
  durationMinutes: 90,
);

await DatabaseService.instance.saveSession(session);
```

### 3. 获取统计数据

```dart
final statisticsData = await DatabaseService.instance.getStatisticsData();
print('今日专注时长: ${statisticsData.getTodayFocusMinutes()}分钟');
```

### 4. 管理设置

```dart
// 获取设置
final settings = await DatabaseService.instance.getSettings();

// 保存设置
final newSettings = settings.copyWith(focusDurationMinutes: 120);
await DatabaseService.instance.saveSettings(newSettings);
```

## 数据迁移

数据库支持版本迁移，当需要更新表结构时：

1. 在`DatabaseMigrations`中增加新版本号
2. 实现对应的迁移方法
3. 应用会自动检测并执行迁移

## 性能优化

1. **索引**: 为常用查询字段创建索引
2. **缓存**: 使用statistics_cache表缓存复杂查询
3. **分页**: 大数据量查询支持分页
4. **异步**: 所有数据库操作都是异步的

## 错误处理

1. **数据库初始化失败**: 应用会使用默认数据继续运行
2. **数据保存失败**: 错误会被记录，不影响UI
3. **数据完整性**: 使用外键约束保证数据一致性

## 调试和维护

### 获取数据库信息
```dart
final info = await DatabaseService.instance.getDatabaseInfo();
print('数据库版本: ${info['version']}');
print('表数量: ${info['tables'].length}');
```

### 清理缓存
```dart
await DatabaseService.instance.clearCache();
```

### 重置数据库（仅用于开发）
```dart
await DatabaseService.instance.resetDatabase();
```

## 最佳实践

1. **始终使用DatabaseService**: 不要直接调用DAO或Repository
2. **异步处理**: 所有数据库操作都应该异步处理
3. **错误处理**: 适当处理数据库操作可能的异常
4. **事务**: 复杂操作使用事务保证数据一致性
5. **测试**: 为数据库操作编写单元测试

## 扩展指南

### 添加新表
1. 在`DatabaseTables`中定义表结构
2. 在`DatabaseMigrations`中添加创建语句
3. 创建对应的DAO类
4. 在Repository中添加业务方法

### 添加新字段
1. 创建新的迁移版本
2. 在迁移中添加ALTER TABLE语句
3. 更新模型类和DAO方法

这个数据库实现提供了完整的数据持久化解决方案，支持应用的所有数据需求，并为未来扩展提供了良好的基础。
