import '../models/focus_session.dart';
import '../repositories/focus_repository.dart';
import '../utils/constants.dart';

// 会话数据管理器 - 负责在关键节点实时记录数据
class SessionDataManager {
  static SessionDataManager? _instance;
  
  final FocusRepository _focusRepository = FocusRepository.instance;
  
  // 当前活跃会话
  FocusSession? _currentSession;
  
  // 单例模式
  SessionDataManager._internal();

  static SessionDataManager get instance {
    _instance ??= SessionDataManager._internal();
    return _instance!;
  }

  // 开始新会话
  Future<void> startSession(FocusSession session) async {
    print('SessionDataManager: Starting session ${session.id}');
    
    _currentSession = session;
    
    // 立即保存会话开始记录
    try {
      await _focusRepository.saveSession(session);
      print('SessionDataManager: Session start recorded successfully');
    } catch (e) {
      print('SessionDataManager: Failed to record session start: $e');
      // 记录失败不影响会话继续
    }
  }

  // 记录小休开始
  Future<void> recordBreakStart(BreakRecord breakRecord) async {
    if (_currentSession == null) {
      print('SessionDataManager: No active session for break start');
      return;
    }

    print('SessionDataManager: Recording break start at ${breakRecord.startTime}');
    
    try {
      // 添加休息记录到当前会话
      _currentSession!.addBreak(breakRecord);
      
      // 更新数据库中的会话记录
      await _focusRepository.updateSession(_currentSession!);
      
      print('SessionDataManager: Break start recorded successfully');
    } catch (e) {
      print('SessionDataManager: Failed to record break start: $e');
      // 记录失败不影响休息继续
    }
  }

  // 记录小休结束
  Future<void> recordBreakEnd() async {
    if (_currentSession == null) {
      print('SessionDataManager: No active session for break end');
      return;
    }

    print('SessionDataManager: Recording break end');
    
    try {
      // 更新最后一个休息记录的结束时间（如果需要的话）
      // 当前实现中，休息记录在开始时就包含了持续时间，所以这里主要是更新会话状态
      
      // 更新数据库中的会话记录
      await _focusRepository.updateSession(_currentSession!);
      
      print('SessionDataManager: Break end recorded successfully');
    } catch (e) {
      print('SessionDataManager: Failed to record break end: $e');
      // 记录失败不影响专注继续
    }
  }

  // 记录会话进度更新（每分钟或关键时刻）
  Future<void> recordProgressUpdate() async {
    if (_currentSession == null) {
      print('SessionDataManager: No active session for progress update');
      return;
    }

    try {
      // 更新数据库中的会话进度
      await _focusRepository.updateSession(_currentSession!);
      
      print('SessionDataManager: Progress update recorded - ${_currentSession!.elapsedMinutes}:${_currentSession!.elapsedSeconds.toString().padLeft(2, '0')}');
    } catch (e) {
      print('SessionDataManager: Failed to record progress update: $e');
      // 记录失败不影响会话继续
    }
  }

  // 记录会话暂停
  Future<void> recordSessionPause() async {
    if (_currentSession == null) {
      print('SessionDataManager: No active session for pause');
      return;
    }

    print('SessionDataManager: Recording session pause');
    
    try {
      _currentSession!.pause();
      await _focusRepository.updateSession(_currentSession!);
      
      print('SessionDataManager: Session pause recorded successfully');
    } catch (e) {
      print('SessionDataManager: Failed to record session pause: $e');
    }
  }

  // 记录会话恢复
  Future<void> recordSessionResume() async {
    if (_currentSession == null) {
      print('SessionDataManager: No active session for resume');
      return;
    }

    print('SessionDataManager: Recording session resume');
    
    try {
      _currentSession!.resume();
      await _focusRepository.updateSession(_currentSession!);
      
      print('SessionDataManager: Session resume recorded successfully');
    } catch (e) {
      print('SessionDataManager: Failed to record session resume: $e');
    }
  }

  // 记录会话完成
  Future<void> recordSessionComplete() async {
    if (_currentSession == null) {
      print('SessionDataManager: No active session for completion');
      return;
    }

    print('SessionDataManager: Recording session completion');
    
    try {
      _currentSession!.complete();
      await _focusRepository.updateSession(_currentSession!);
      
      print('SessionDataManager: Session completion recorded successfully');
      
      // 清除当前会话
      _currentSession = null;
    } catch (e) {
      print('SessionDataManager: Failed to record session completion: $e');
      // 即使记录失败，也清除当前会话
      _currentSession = null;
    }
  }

  // 记录会话取消
  Future<void> recordSessionCancel() async {
    if (_currentSession == null) {
      print('SessionDataManager: No active session for cancellation');
      return;
    }

    print('SessionDataManager: Recording session cancellation');
    
    try {
      _currentSession!.cancel();
      await _focusRepository.updateSession(_currentSession!);
      
      print('SessionDataManager: Session cancellation recorded successfully');
      
      // 清除当前会话
      _currentSession = null;
    } catch (e) {
      print('SessionDataManager: Failed to record session cancellation: $e');
      // 即使记录失败，也清除当前会话
      _currentSession = null;
    }
  }

  // 获取当前会话
  FocusSession? get currentSession => _currentSession;

  // 检查是否有活跃会话
  bool get hasActiveSession => _currentSession != null;

  // 定期保存会话数据（可选，用于防止数据丢失）
  Future<void> periodicSave() async {
    if (_currentSession == null) return;

    try {
      await _focusRepository.updateSession(_currentSession!);
      print('SessionDataManager: Periodic save completed');
    } catch (e) {
      print('SessionDataManager: Periodic save failed: $e');
    }
  }

  // 清理会话（用于异常情况）
  void clearSession() {
    print('SessionDataManager: Clearing current session');
    _currentSession = null;
  }
}
