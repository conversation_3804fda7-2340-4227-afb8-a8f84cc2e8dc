import '../database/database_helper.dart';
import '../repositories/focus_repository.dart';
import '../repositories/statistics_repository.dart';
import '../models/settings.dart';
import '../models/statistics.dart';
import '../models/focus_session.dart';

// 数据库服务类
class DatabaseService {
  static DatabaseService? _instance;

  final FocusRepository _focusRepository = FocusRepository.instance;
  final StatisticsRepository _statisticsRepository = StatisticsRepository.instance;

  bool _isInitialized = false;

  // 单例模式
  DatabaseService._internal();

  static DatabaseService get instance {
    _instance ??= DatabaseService._internal();
    return _instance!;
  }

  // 初始化数据库服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('DatabaseService: Initializing database service');

      // 初始化数据库
      await DatabaseHelper.instance.database;

      _isInitialized = true;
      print('DatabaseService: Database service initialized successfully');
    } catch (e) {
      print('DatabaseService: Failed to initialize database service: $e');
      rethrow;
    }
  }

  // 获取应用设置
  Future<AppSettings> getSettings() async {
    await _ensureInitialized();
    return await _focusRepository.getSettings();
  }

  // 保存应用设置
  Future<void> saveSettings(AppSettings settings) async {
    await _ensureInitialized();
    await _focusRepository.saveSettings(settings);
  }

  // 获取统计数据
  Future<StatisticsData> getStatisticsData() async {
    await _ensureInitialized();
    return await _statisticsRepository.getStatisticsData();
  }

  // 保存专注会话
  Future<void> saveSession(FocusSession session) async {
    await _ensureInitialized();
    await _focusRepository.saveSession(session);
  }

  // 获取数据库信息（用于调试）
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    await _ensureInitialized();
    return await DatabaseHelper.instance.getDatabaseInfo();
  }

  // 清理缓存
  Future<void> clearCache() async {
    await _ensureInitialized();
    await DatabaseHelper.instance.clearCache();
  }

  // 重置数据库（用于测试）
  Future<void> resetDatabase() async {
    await DatabaseHelper.instance.reset();
    _isInitialized = false;
  }

  // 确保数据库已初始化
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  // 检查数据库健康状态
  Future<bool> checkHealth() async {
    try {
      await _ensureInitialized();

      // 尝试执行一个简单的查询
      final info = await getDatabaseInfo();
      return info.isNotEmpty;
    } catch (e) {
      print('DatabaseService: Health check failed: $e');
      return false;
    }
  }
}
