import 'package:flutter/material.dart';
import 'utils/constants.dart';
import 'models/focus_session.dart';
import 'models/settings.dart';
import 'models/statistics.dart';
import 'screens/home_screen.dart';
import 'screens/focus_screen.dart';
import 'screens/statistics_screen.dart';
import 'screens/settings_screen.dart';
import 'widgets/bottom_navigation.dart';
import 'services/audio_service.dart';
import 'services/database_service.dart';

void main() {
  runApp(const InterflowApp());
}

class InterflowApp extends StatelessWidget {
  const InterflowApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '间歇流',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(AppConstants.primaryColor),
        ),
        useMaterial3: true,
        fontFamily: 'PingFang SC',
      ),
      debugShowCheckedModeBanner: false,
      home: const MainScreen(),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  AppState _appState = AppState.idle;

  // 数据模型
  late AppSettings _settings;
  late StatisticsData _statisticsData;
  FocusSession? _currentSession;

  // 数据库服务
  final DatabaseService _databaseService = DatabaseService.instance;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    // 初始化默认数据（将被数据库数据覆盖）
    _settings = AppSettings();
    _statisticsData = StatisticsData();

    // 异步初始化音频服务，不阻塞UI
    _initializeAudioService();

    // 异步加载数据库数据，不阻塞UI
    _loadDataFromDatabase();
  }

  /// 从数据库加载数据（异步，不阻塞UI）
  void _loadDataFromDatabase() {
    Future.microtask(() async {
      try {
        print('Main: Loading data from database');

        // 初始化数据库服务
        await _databaseService.initialize();

        // 检查数据库健康状态
        final isHealthy = await _databaseService.checkHealth();
        print('Main: Database health check: $isHealthy');

        // 获取数据库信息用于调试
        final dbInfo = await _databaseService.getDatabaseInfo();
        print('Main: Database info: $dbInfo');

        // 如果数据库中没有已完成的会话数据，插入一些测试数据
        final completedSessionsCount = await _getCompletedSessionsCount();
        print('Main: Found ${dbInfo['focus_sessions_count']} total sessions, $completedSessionsCount completed sessions');

        if (completedSessionsCount == 0) {
          print('Main: No completed sessions found, creating test data');
          await _createTestData();
        }

        // 加载设置
        final settings = await _databaseService.getSettings();

        // 加载统计数据
        final statisticsData = await _databaseService.getStatisticsData();

        // 更新UI
        if (mounted) {
          setState(() {
            _settings = settings;
            _statisticsData = statisticsData;
          });
        }

        print('Main: Data loaded from database successfully');
      } catch (e) {
        print('Main: Failed to load data from database: $e');
        print('Main: Stack trace: ${StackTrace.current}');
        // 数据库加载失败不影响应用启动，使用默认数据
      }
    });
  }

  /// 获取已完成会话数量
  Future<int> _getCompletedSessionsCount() async {
    try {
      final statisticsData = await _databaseService.getStatisticsData();
      final completedSessions = statisticsData.sessions.where((s) => s.isCompleted);
      return completedSessions.length;
    } catch (e) {
      print('Main: Failed to get completed sessions count: $e');
      return 0;
    }
  }

  /// 创建测试数据（仅在数据库为空时）
  Future<void> _createTestData() async {
    try {
      print('Main: Creating test data');

      // 创建一个已完成的测试会话（今天）
      final todaySession = FocusSession(
        id: 'test_session_today_${DateTime.now().millisecondsSinceEpoch}',
        startTime: DateTime.now().subtract(const Duration(hours: 2)),
        durationMinutes: 90,
      );
      todaySession.elapsedMinutes = 90;
      todaySession.elapsedSeconds = 0;
      todaySession.complete();

      // 添加一些休息记录
      todaySession.addBreak(BreakRecord(
        startTime: DateTime.now().subtract(const Duration(hours: 2, minutes: 30)),
        durationSeconds: 8,
        reactionTimeSeconds: 1.2,
      ));
      todaySession.addBreak(BreakRecord(
        startTime: DateTime.now().subtract(const Duration(hours: 2, minutes: 15)),
        durationSeconds: 10,
        reactionTimeSeconds: 0.8,
      ));

      await _databaseService.saveSession(todaySession);
      print('Main: Test session created: ${todaySession.id}');

      // 创建一个昨天的测试会话
      final yesterdaySession = FocusSession(
        id: 'test_session_yesterday_${DateTime.now().millisecondsSinceEpoch}',
        startTime: DateTime.now().subtract(const Duration(days: 1, hours: 3)),
        durationMinutes: 90,
      );
      yesterdaySession.elapsedMinutes = 75;
      yesterdaySession.elapsedSeconds = 0;
      yesterdaySession.complete();

      await _databaseService.saveSession(yesterdaySession);
      print('Main: Yesterday test session created: ${yesterdaySession.id}');

      print('Main: Test data creation completed');
    } catch (e) {
      print('Main: Failed to create test data: $e');
    }
  }

  /// 异步初始化音频服务（不阻塞UI）
  void _initializeAudioService() {
    print('Main: Starting background audio service initialization');
    // 在后台异步初始化，不等待完成，不阻塞UI
    Future.microtask(() async {
      try {
        print('Main: Calling AudioService.instance.initialize()');
        await AudioService.instance.initialize();
        print('Main: Audio service initialized successfully');
      } catch (e) {
        print('Main: Audio service initialization failed: $e');
        print('Main: Audio service will use fallback mode');
        // 初始化失败不影响应用启动，音频服务会使用回退模式
      }
    });
  }

  void _startFocusSession() async {
    // 检查widget是否仍然mounted
    if (!mounted) return;

    final session = FocusSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      startTime: DateTime.now(),
      durationMinutes: _settings.focusDurationMinutes,
    );

    setState(() {
      _currentSession = session;
      _appState = AppState.focusing;
    });

    // 导航到专注界面（通过覆盖当前界面）
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => FocusScreen(
          session: session,
          settings: _settings,
          onPause: () {
            setState(() {
              _appState = AppState.idle;
            });
          },
          onResume: () {
            setState(() {
              _appState = AppState.focusing;
            });
          },
          onStop: () {
            setState(() {
              _currentSession = null;
              _appState = AppState.idle;
            });
            Navigator.of(context).pop();
          },
          onBreakTriggered: (breakRecord) {
            // 处理休息触发
          },
          onSessionComplete: () async {
            if (_currentSession != null) {
              // 重新加载统计数据（会话已在SessionDataManager中保存）
              final updatedStatistics = await _databaseService.getStatisticsData();

              if (mounted) {
                setState(() {
                  _statisticsData = updatedStatistics;
                  _currentSession = null;
                  _appState = AppState.completed;
                });

                Navigator.of(context).pop();
                _showCompletionDialog();
              }
            }
          },
        ),
      ),
    );
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        ),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.paddingXLarge),
          decoration: BoxDecoration(
            color: const Color(AppConstants.cardColor),
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 成功图标
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(AppConstants.successColor),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 40,
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // 标题
              const Text(
                '专注完成！',
                style: TextStyle(
                  fontSize: AppConstants.titleFontSize,
                  fontWeight: FontWeight.bold,
                  color: Color(AppConstants.textPrimaryColor),
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // 主要信息
              const Text(
                '🎉 恭喜完成90分钟专注',
                style: TextStyle(
                  fontSize: AppConstants.bodyFontSize,
                  color: Color(AppConstants.textPrimaryColor),
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppConstants.paddingSmall),

              const Text(
                '现在开始20分钟完整休息',
                style: TextStyle(
                  fontSize: AppConstants.bodyFontSize,
                  color: Color(AppConstants.textSecondaryColor),
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // 统计信息
              if (_currentSession != null) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  decoration: BoxDecoration(
                    color: const Color(AppConstants.backgroundColor),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        '本次专注数据统计',
                        style: TextStyle(
                          fontSize: AppConstants.captionFontSize,
                          fontWeight: FontWeight.w600,
                          color: Color(AppConstants.textPrimaryColor),
                        ),
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      Text(
                        '小休次数: ${_currentSession!.totalBreaks}次',
                        style: const TextStyle(
                          fontSize: AppConstants.captionFontSize,
                          color: Color(AppConstants.textSecondaryColor),
                        ),
                      ),
                      Text(
                        '平均间隔: ${_currentSession!.averageBreakInterval.toStringAsFixed(1)}分钟',
                        style: const TextStyle(
                          fontSize: AppConstants.captionFontSize,
                          color: Color(AppConstants.textSecondaryColor),
                        ),
                      ),
                      Text(
                        '总休息时长: ${_currentSession!.totalBreakDuration}秒',
                        style: const TextStyle(
                          fontSize: AppConstants.captionFontSize,
                          color: Color(AppConstants.textSecondaryColor),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppConstants.paddingLarge),
              ],

              // 按钮
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        setState(() {
                          _appState = AppState.idle;
                        });
                      },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
                      ),
                      child: const Text('知道了'),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        // TODO: 开始休息计时
                        setState(() {
                          _appState = AppState.longBreak;
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(AppConstants.primaryColor),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                        ),
                      ),
                      child: const Text('开始休息计时'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onTabChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  void _onSettingsChanged(AppSettings newSettings) {
    setState(() {
      _settings = newSettings;
    });

    // 异步保存设置到数据库
    _databaseService.saveSettings(newSettings).catchError((e) {
      print('Failed to save settings: $e');
    });
  }

  @override
  Widget build(BuildContext context) {
    // 调试模式：直接显示小休息界面
    if (AppConstants.debugShowBreakScreen) {
      return _buildDebugBreakScreen();
    }

    // 调试模式：直接显示完成界面
    if (AppConstants.debugShowCompletionScreen) {
      return _buildDebugCompletionScreen();
    }

    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: [
          // 主页
          HomeScreen(
            statisticsData: _statisticsData,
            settings: _settings,
            onStartFocus: _startFocusSession,
          ),

          // 统计页面
          StatisticsScreen(
            statisticsData: _statisticsData,
          ),

          // 设置页面
          SettingsScreen(
            settings: _settings,
            onSettingsChanged: _onSettingsChanged,
          ),
        ],
      ),
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: _currentIndex,
        onTap: _onTabChanged,
      ),
    );
  }

  // 调试用的小休息界面
  Widget _buildDebugBreakScreen() {
    return Scaffold(
      backgroundColor: const Color(AppConstants.backgroundColor),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 调试标识
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingMedium,
                  vertical: AppConstants.paddingSmall,
                ),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                  border: Border.all(color: Colors.red, width: 1),
                ),
                child: const Text(
                  '调试模式 - 小休息界面',
                  style: TextStyle(
                    fontSize: AppConstants.captionFontSize,
                    color: Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              const SizedBox(height: AppConstants.paddingXLarge),

              // 标题
              const Text(
                '休息时间',
                style: TextStyle(
                  fontSize: AppConstants.titleFontSize + 4,
                  fontWeight: FontWeight.bold,
                  color: Color(AppConstants.textPrimaryColor),
                ),
              ),

              const SizedBox(height: AppConstants.paddingXLarge * 1.5),

              // 倒计时圆圈（静态显示）
              Container(
                width: 180,
                height: 180,
                decoration: BoxDecoration(
                  color: const Color(AppConstants.warningColor),
                  borderRadius: BorderRadius.circular(90),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(AppConstants.warningColor).withOpacity(0.4),
                      blurRadius: 30,
                      offset: const Offset(0, 15),
                    ),
                  ],
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '8',
                        style: TextStyle(
                          fontSize: 42,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '秒',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: AppConstants.paddingXLarge * 1.5),

              // 提示文字
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.paddingLarge),
                margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: const Color(AppConstants.cardColor),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Text(
                  '请闭眼深呼吸放松\n让眼睛得到充分休息',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: AppConstants.bodyFontSize,
                    height: 1.5,
                    color: Color(AppConstants.textPrimaryColor),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 调试用的完成界面
  Widget _buildDebugCompletionScreen() {
    return Scaffold(
      backgroundColor: const Color(AppConstants.backgroundColor),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingLarge),
          child: Container(
            padding: const EdgeInsets.all(AppConstants.paddingXLarge),
            decoration: BoxDecoration(
              color: const Color(AppConstants.cardColor),
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 调试标识
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingMedium,
                    vertical: AppConstants.paddingSmall,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                    border: Border.all(color: Colors.red, width: 1),
                  ),
                  child: const Text(
                    '调试模式 - 完成界面',
                    style: TextStyle(
                      fontSize: AppConstants.captionFontSize,
                      color: Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // 成功图标
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: const Color(AppConstants.successColor),
                    borderRadius: BorderRadius.circular(40),
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 40,
                  ),
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // 标题
                const Text(
                  '专注完成！',
                  style: TextStyle(
                    fontSize: AppConstants.titleFontSize,
                    fontWeight: FontWeight.bold,
                    color: Color(AppConstants.textPrimaryColor),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // 主要信息
                const Text(
                  '🎉 恭喜完成90分钟专注',
                  style: TextStyle(
                    fontSize: AppConstants.bodyFontSize,
                    color: Color(AppConstants.textPrimaryColor),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: AppConstants.paddingSmall),

                const Text(
                  '现在开始20分钟完整休息',
                  style: TextStyle(
                    fontSize: AppConstants.bodyFontSize,
                    color: Color(AppConstants.textSecondaryColor),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // 模拟统计信息
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  decoration: BoxDecoration(
                    color: const Color(AppConstants.backgroundColor),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  child: const Column(
                    children: [
                      Text(
                        '本次专注数据统计',
                        style: TextStyle(
                          fontSize: AppConstants.captionFontSize,
                          fontWeight: FontWeight.w600,
                          color: Color(AppConstants.textPrimaryColor),
                        ),
                      ),
                      SizedBox(height: AppConstants.paddingSmall),
                      Text(
                        '小休次数: 18次',
                        style: TextStyle(
                          fontSize: AppConstants.captionFontSize,
                          color: Color(AppConstants.textSecondaryColor),
                        ),
                      ),
                      Text(
                        '平均间隔: 4.2分钟',
                        style: TextStyle(
                          fontSize: AppConstants.captionFontSize,
                          color: Color(AppConstants.textSecondaryColor),
                        ),
                      ),
                      Text(
                        '总休息时长: 162秒',
                        style: TextStyle(
                          fontSize: AppConstants.captionFontSize,
                          color: Color(AppConstants.textSecondaryColor),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // 按钮
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () {
                          // 调试模式下不做任何操作
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
                        ),
                        child: const Text('知道了'),
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          // 调试模式下不做任何操作
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(AppConstants.primaryColor),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                          ),
                        ),
                        child: const Text('开始休息计时'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
