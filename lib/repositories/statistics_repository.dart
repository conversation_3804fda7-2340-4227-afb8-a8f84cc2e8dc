import '../dao/focus_session_dao.dart';
import '../dao/achievement_dao.dart';
import '../models/focus_session.dart';
import '../models/statistics.dart';

// 统计数据仓库
class StatisticsRepository {
  static StatisticsRepository? _instance;

  final FocusSessionDao _sessionDao = FocusSessionDao();
  final AchievementDao _achievementDao = AchievementDao();

  // 单例模式
  StatisticsRepository._internal();

  static StatisticsRepository get instance {
    _instance ??= StatisticsRepository._internal();
    return _instance!;
  }

  // 获取完整的统计数据
  Future<StatisticsData> getStatisticsData() async {
    print('StatisticsRepository: Getting statistics data');

    final sessions = await _sessionDao.getAllSessions();
    print('StatisticsRepository: Retrieved ${sessions.length} sessions from database');

    // 打印会话详情用于调试
    for (int i = 0; i < sessions.length && i < 5; i++) {
      final session = sessions[i];
      print('StatisticsRepository: Session $i - ID: ${session.id}, completed: ${session.isCompleted}, elapsed: ${session.elapsedMinutes}min, breaks: ${session.breaks.length}');
    }

    final achievements = await _achievementDao.getAchievementMap();
    print('StatisticsRepository: Retrieved ${achievements.length} achievements from database');

    final mindPoints = _calculateMindPoints(sessions, achievements);
    print('StatisticsRepository: Calculated mind points: $mindPoints');

    final statisticsData = StatisticsData(
      sessions: sessions,
      achievements: achievements,
      mindPoints: mindPoints,
    );

    // 验证统计数据计算
    print('StatisticsRepository: Statistics verification - Today: ${statisticsData.getTodayFocusMinutes()}min, Week: ${statisticsData.getWeekFocusMinutes()}min, Month: ${statisticsData.getMonthFocusMinutes()}min');
    print('StatisticsRepository: Completion rate: ${statisticsData.getCompletionRate()}, Total breaks: ${statisticsData.getTotalBreaks()}');

    return statisticsData;
  }

  // 获取今日专注时长（分钟）
  Future<int> getTodayFocusMinutes() async {
    final sessions = await _sessionDao.getTodaySessions();
    final completedSessions = sessions.where((s) => s.isCompleted);
    return completedSessions.fold<int>(0, (sum, session) => sum + session.elapsedMinutes);
  }

  // 获取本周专注时长（分钟）
  Future<int> getWeekFocusMinutes() async {
    final sessions = await _sessionDao.getWeekSessions();
    final completedSessions = sessions.where((s) => s.isCompleted);
    return completedSessions.fold<int>(0, (sum, session) => sum + session.elapsedMinutes);
  }

  // 获取本月专注时长（分钟）
  Future<int> getMonthFocusMinutes() async {
    final sessions = await _sessionDao.getMonthSessions();
    final completedSessions = sessions.where((s) => s.isCompleted);
    return completedSessions.fold<int>(0, (sum, session) => sum + session.elapsedMinutes);
  }

  // 获取专注完成率
  Future<double> getCompletionRate() async {
    final sessions = await _sessionDao.getAllSessions();
    if (sessions.isEmpty) return 0.0;

    final completedSessions = sessions.where((s) => s.isCompleted).length;
    return completedSessions / sessions.length;
  }

  // 获取平均小休间隔（分钟）
  Future<double> getAverageBreakInterval() async {
    final sessions = await _sessionDao.getAllSessions();
    final completedSessions = sessions.where((s) => s.isCompleted);

    if (completedSessions.isEmpty) return 0.0;

    final totalInterval = completedSessions.fold(0.0, (sum, session) =>
        sum + session.averageBreakInterval);

    return totalInterval / completedSessions.length;
  }

  // 获取平均小休反应时间（秒）
  Future<double> getAverageReactionTime() async {
    final sessions = await _sessionDao.getAllSessions();
    final allBreaks = sessions.expand((session) => session.breaks);

    if (allBreaks.isEmpty) return 0.0;

    final totalReactionTime = allBreaks.fold(0.0, (sum, breakRecord) =>
        sum + breakRecord.reactionTimeSeconds);

    return totalReactionTime / allBreaks.length;
  }

  // 获取总小休次数
  Future<int> getTotalBreaks() async {
    final sessions = await _sessionDao.getAllSessions();
    return sessions.fold<int>(0, (sum, session) => sum + session.totalBreaks);
  }

  // 获取连续专注天数
  Future<int> getStreakDays() async {
    final sessions = await _sessionDao.getAllSessions();
    final completedSessions = sessions.where((s) => s.isCompleted).toList();

    if (completedSessions.isEmpty) return 0;

    // 按日期分组
    final sessionsByDate = <String, List<FocusSession>>{};
    for (final session in completedSessions) {
      final dateKey = '${session.startTime.year}-${session.startTime.month.toString().padLeft(2, '0')}-${session.startTime.day.toString().padLeft(2, '0')}';
      sessionsByDate.putIfAbsent(dateKey, () => []).add(session);
    }

    // 获取有专注记录的日期列表并排序
    final dates = sessionsByDate.keys.toList()..sort();
    if (dates.isEmpty) return 0;

    // 从最近的日期开始计算连续天数
    int streak = 1;
    for (int i = dates.length - 2; i >= 0; i--) {
      final currentDate = DateTime.parse(dates[i + 1]);
      final previousDate = DateTime.parse(dates[i]);

      // 检查是否是连续的天
      if (currentDate.difference(previousDate).inDays == 1) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  // 获取每日专注时长数据（最近7天）
  Future<List<DailyFocusData>> getWeeklyFocusData() async {
    final now = DateTime.now();
    final weekData = <DailyFocusData>[];

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));

      final daySessions = await _sessionDao.getSessionsByDateRange(
        dayStart,
        dayEnd,
        completedOnly: true,
      );

      final totalMinutes = daySessions.fold(0, (sum, session) => sum + session.elapsedMinutes);

      weekData.add(DailyFocusData(
        date: dayStart,
        focusMinutes: totalMinutes,
        sessionCount: daySessions.length,
      ));
    }

    return weekData;
  }

  // 获取月度专注数据（最近30天）
  Future<List<DailyFocusData>> getMonthlyFocusData() async {
    final now = DateTime.now();
    final monthData = <DailyFocusData>[];

    for (int i = 29; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayStart = DateTime(date.year, date.month, date.day);
      final dayEnd = dayStart.add(const Duration(days: 1));

      final daySessions = await _sessionDao.getSessionsByDateRange(
        dayStart,
        dayEnd,
        completedOnly: true,
      );

      final totalMinutes = daySessions.fold(0, (sum, session) => sum + session.elapsedMinutes);

      monthData.add(DailyFocusData(
        date: dayStart,
        focusMinutes: totalMinutes,
        sessionCount: daySessions.length,
      ));
    }

    return monthData;
  }

  // 获取成就统计
  Future<Map<String, dynamic>> getAchievementStatistics() async {
    return await _achievementDao.getAchievementStatistics();
  }

  // 获取最近解锁的成就
  Future<List<Achievement>> getRecentAchievements({int limit = 3}) async {
    return await _achievementDao.getRecentAchievements(limit: limit);
  }

  // 获取所有成就
  Future<Map<String, Achievement>> getAllAchievements() async {
    return await _achievementDao.getAchievementMap();
  }

  // 获取专注质量分析
  Future<Map<String, dynamic>> getFocusQualityAnalysis() async {
    final completionRate = await getCompletionRate();
    final avgBreakInterval = await getAverageBreakInterval();
    final avgReactionTime = await getAverageReactionTime();
    final streakDays = await getStreakDays();
    final totalBreaks = await getTotalBreaks();

    return {
      'completion_rate': completionRate,
      'average_break_interval': avgBreakInterval,
      'average_reaction_time': avgReactionTime,
      'streak_days': streakDays,
      'total_breaks': totalBreaks,
    };
  }

  // 获取时间统计摘要
  Future<Map<String, int>> getTimeStatisticsSummary() async {
    final todayMinutes = await getTodayFocusMinutes();
    final weekMinutes = await getWeekFocusMinutes();
    final monthMinutes = await getMonthFocusMinutes();

    return {
      'today': todayMinutes,
      'week': weekMinutes,
      'month': monthMinutes,
    };
  }

  // 计算心智积分
  int _calculateMindPoints(List<FocusSession> sessions, Map<String, Achievement> achievements) {
    int points = 0;

    // 完成会话获得积分
    final completedSessions = sessions.where((s) => s.isCompleted);
    points += completedSessions.length * 10; // 每完成一次专注获得10分

    // 成就获得积分
    points += achievements.length * 50; // 每个成就获得50分

    // 连续天数奖励
    final streakDays = _calculateStreakDays(sessions);
    points += streakDays * 5; // 每连续一天获得5分

    return points;
  }

  // 计算连续专注天数（内部方法）
  int _calculateStreakDays(List<FocusSession> sessions) {
    final completedSessions = sessions.where((s) => s.isCompleted).toList();

    if (completedSessions.isEmpty) return 0;

    // 按日期分组
    final sessionsByDate = <String, List<FocusSession>>{};
    for (final session in completedSessions) {
      final dateKey = '${session.startTime.year}-${session.startTime.month.toString().padLeft(2, '0')}-${session.startTime.day.toString().padLeft(2, '0')}';
      sessionsByDate.putIfAbsent(dateKey, () => []).add(session);
    }

    // 获取有专注记录的日期列表并排序
    final dates = sessionsByDate.keys.toList()..sort();
    if (dates.isEmpty) return 0;

    // 从最近的日期开始计算连续天数
    int streak = 1;
    for (int i = dates.length - 2; i >= 0; i--) {
      final currentDate = DateTime.parse(dates[i + 1]);
      final previousDate = DateTime.parse(dates[i]);

      // 检查是否是连续的天
      if (currentDate.difference(previousDate).inDays == 1) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }
}
