import '../dao/focus_session_dao.dart';
import '../dao/achievement_dao.dart';
import '../dao/settings_dao.dart';
import '../models/focus_session.dart';
import '../models/settings.dart';
import '../models/statistics.dart';
import '../utils/constants.dart';

// 专注数据仓库
class FocusRepository {
  static FocusRepository? _instance;

  final FocusSessionDao _sessionDao = FocusSessionDao();
  final AchievementDao _achievementDao = AchievementDao();
  final SettingsDao _settingsDao = SettingsDao();

  // 单例模式
  FocusRepository._internal();

  static FocusRepository get instance {
    _instance ??= FocusRepository._internal();
    return _instance!;
  }

  // 保存专注会话
  Future<void> saveSession(FocusSession session) async {
    print('FocusRepository.saveSession: Saving session ${session.id}');
    await _sessionDao.insertSession(session);
    print('FocusRepository.saveSession: Session ${session.id} saved successfully');

    // 如果会话完成，检查并更新成就
    if (session.isCompleted) {
      print('FocusRepository.saveSession: Session completed, checking achievements');
      await _checkAndUpdateAchievements();
    }
  }

  // 更新专注会话
  Future<void> updateSession(FocusSession session) async {
    print('FocusRepository.updateSession: Updating session ${session.id}');
    await _sessionDao.updateSession(session);
    print('FocusRepository.updateSession: Session ${session.id} updated successfully');

    // 如果会话完成，检查并更新成就
    if (session.isCompleted) {
      print('FocusRepository.updateSession: Session completed, checking achievements');
      await _checkAndUpdateAchievements();
    }
  }

  // 获取专注会话
  Future<FocusSession?> getSession(String id) async {
    return await _sessionDao.getSessionById(id);
  }

  // 获取所有会话
  Future<List<FocusSession>> getAllSessions({
    int? limit,
    int? offset,
  }) async {
    return await _sessionDao.getAllSessions(
      limit: limit,
      offset: offset,
    );
  }

  // 获取今日会话
  Future<List<FocusSession>> getTodaySessions() async {
    return await _sessionDao.getTodaySessions();
  }

  // 获取本周会话
  Future<List<FocusSession>> getWeekSessions() async {
    return await _sessionDao.getWeekSessions();
  }

  // 获取本月会话
  Future<List<FocusSession>> getMonthSessions() async {
    return await _sessionDao.getMonthSessions();
  }

  // 获取指定日期范围的会话
  Future<List<FocusSession>> getSessionsByDateRange(
    DateTime startDate,
    DateTime endDate, {
    bool? completedOnly,
  }) async {
    return await _sessionDao.getSessionsByDateRange(
      startDate,
      endDate,
      completedOnly: completedOnly,
    );
  }

  // 删除会话
  Future<void> deleteSession(String id) async {
    await _sessionDao.deleteSession(id);
  }

  // 获取应用设置
  Future<AppSettings> getSettings() async {
    return await _settingsDao.getSettings();
  }

  // 保存应用设置
  Future<void> saveSettings(AppSettings settings) async {
    await _settingsDao.saveSettings(settings);
  }

  // 获取所有成就
  Future<Map<String, Achievement>> getAchievements() async {
    return await _achievementDao.getAchievementMap();
  }

  // 获取最近成就
  Future<List<Achievement>> getRecentAchievements({int limit = 5}) async {
    return await _achievementDao.getRecentAchievements(limit: limit);
  }

  // 检查并更新成就
  Future<List<Achievement>> _checkAndUpdateAchievements() async {
    final newAchievements = <Achievement>[];
    final existingAchievements = await _achievementDao.getAchievementMap();

    // 获取所有完成的会话
    final allSessions = await _sessionDao.getAllSessions();
    final completedSessions = allSessions.where((s) => s.isCompleted).toList();

    if (completedSessions.isEmpty) return newAchievements;

    // 检查首次完成成就
    if (!existingAchievements.containsKey('first_session')) {
      final achievement = Achievement(
        id: 'first_session',
        name: '初学者',
        description: '完成首个90分钟专注',
        type: AchievementType.firstSession,
        unlockedAt: DateTime.now(),
      );
      await _achievementDao.insertAchievement(achievement);
      newAchievements.add(achievement);
    }

    // 检查连续天数成就
    final streakDays = _calculateStreakDays(completedSessions);
    if (streakDays >= 3 && !existingAchievements.containsKey('three_day_streak')) {
      final achievement = Achievement(
        id: 'three_day_streak',
        name: '坚持者',
        description: '连续专注3天',
        type: AchievementType.streak,
        unlockedAt: DateTime.now(),
      );
      await _achievementDao.insertAchievement(achievement);
      newAchievements.add(achievement);
    }

    // 检查完成率成就
    final completionRate = completedSessions.length / allSessions.length;
    if (completionRate >= AppConstants.mediumCompletionRate &&
        !existingAchievements.containsKey('completion_rate_70')) {
      final achievement = Achievement(
        id: 'completion_rate_70',
        name: '专注新手',
        description: '完成率达到70%',
        type: AchievementType.completionRate,
        unlockedAt: DateTime.now(),
      );
      await _achievementDao.insertAchievement(achievement);
      newAchievements.add(achievement);
    }

    if (completionRate >= AppConstants.highCompletionRate &&
        !existingAchievements.containsKey('completion_rate_85')) {
      final achievement = Achievement(
        id: 'completion_rate_85',
        name: '专注能手',
        description: '完成率达到85%',
        type: AchievementType.completionRate,
        unlockedAt: DateTime.now(),
      );
      await _achievementDao.insertAchievement(achievement);
      newAchievements.add(achievement);
    }

    // 检查总休息次数成就
    final totalBreaks = completedSessions.fold(0, (sum, session) => sum + session.totalBreaks);
    if (totalBreaks >= AppConstants.hundredBreaksAchievement &&
        !existingAchievements.containsKey('hundred_breaks')) {
      final achievement = Achievement(
        id: 'hundred_breaks',
        name: '专注大师',
        description: '完成100次小休',
        type: AchievementType.totalBreaks,
        unlockedAt: DateTime.now(),
      );
      await _achievementDao.insertAchievement(achievement);
      newAchievements.add(achievement);
    }

    return newAchievements;
  }

  // 计算连续专注天数
  int _calculateStreakDays(List<FocusSession> completedSessions) {
    if (completedSessions.isEmpty) return 0;

    // 按日期分组
    final sessionsByDate = <String, List<FocusSession>>{};
    for (final session in completedSessions) {
      final dateKey = '${session.startTime.year}-${session.startTime.month}-${session.startTime.day}';
      sessionsByDate.putIfAbsent(dateKey, () => []).add(session);
    }

    // 获取有专注记录的日期列表并排序
    final dates = sessionsByDate.keys.toList()..sort();
    if (dates.isEmpty) return 0;

    // 从最近的日期开始计算连续天数
    int streak = 1;
    for (int i = dates.length - 2; i >= 0; i--) {
      final currentDate = DateTime.parse(dates[i + 1]);
      final previousDate = DateTime.parse(dates[i]);

      // 检查是否是连续的天
      if (currentDate.difference(previousDate).inDays == 1) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  // 获取统计数据
  Future<Map<String, dynamic>> getStatistics() async {
    return await _sessionDao.getStatistics();
  }

  // 清理旧数据（可选，用于数据维护）
  Future<void> cleanupOldData({int daysToKeep = 365}) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
    final oldSessions = await _sessionDao.getSessionsByDateRange(
      DateTime(2000), // 很早的日期
      cutoffDate,
    );

    for (final session in oldSessions) {
      await _sessionDao.deleteSession(session.id);
    }
  }
}
