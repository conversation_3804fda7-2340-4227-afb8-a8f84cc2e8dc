// 数据库表定义
class DatabaseTables {
  // 专注会话表
  static const String focusSessions = 'focus_sessions';
  static const String createFocusSessionsTable = '''
    CREATE TABLE $focusSessions (
      id TEXT PRIMARY KEY,
      start_time INTEGER NOT NULL,
      end_time INTEGER,
      duration_minutes INTEGER NOT NULL DEFAULT 90,
      state INTEGER NOT NULL DEFAULT 0,
      elapsed_minutes INTEGER NOT NULL DEFAULT 0,
      elapsed_seconds INTEGER NOT NULL DEFAULT 0,
      is_completed INTEGER NOT NULL DEFAULT 0,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL
    )
  ''';

  // 休息记录表
  static const String breakRecords = 'break_records';
  static const String createBreakRecordsTable = '''
    CREATE TABLE $breakRecords (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      session_id TEXT NOT NULL,
      start_time INTEGER NOT NULL,
      duration_seconds INTEGER NOT NULL,
      reaction_time_seconds REAL NOT NULL DEFAULT 0.0,
      created_at INTEGER NOT NULL,
      FOREIGN KEY (session_id) REFERENCES $focusSessions (id) ON DELETE CASCADE
    )
  ''';

  // 成就表
  static const String achievements = 'achievements';
  static const String createAchievementsTable = '''
    CREATE TABLE $achievements (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT NOT NULL,
      type INTEGER NOT NULL,
      unlocked_at INTEGER NOT NULL,
      created_at INTEGER NOT NULL
    )
  ''';

  // 应用设置表
  static const String appSettings = 'app_settings';
  static const String createAppSettingsTable = '''
    CREATE TABLE $appSettings (
      id INTEGER PRIMARY KEY CHECK (id = 1),
      focus_duration_minutes INTEGER NOT NULL DEFAULT 90,
      use_random_break_interval INTEGER NOT NULL DEFAULT 1,
      fixed_break_interval_minutes INTEGER NOT NULL DEFAULT 4,
      min_break_interval_minutes INTEGER NOT NULL DEFAULT 3,
      max_break_interval_minutes INTEGER NOT NULL DEFAULT 5,
      use_random_break_duration INTEGER NOT NULL DEFAULT 1,
      fixed_break_duration_seconds INTEGER NOT NULL DEFAULT 8,
      min_break_duration_seconds INTEGER NOT NULL DEFAULT 8,
      max_break_duration_seconds INTEGER NOT NULL DEFAULT 10,
      audio_theme TEXT NOT NULL DEFAULT 'default',
      sound_volume REAL NOT NULL DEFAULT 0.7,
      enable_vibration INTEGER NOT NULL DEFAULT 1,
      enable_dark_mode INTEGER NOT NULL DEFAULT 0,
      language TEXT NOT NULL DEFAULT 'zh_CN',
      show_seconds INTEGER NOT NULL DEFAULT 1,
      enable_notifications INTEGER NOT NULL DEFAULT 1,
      daily_focus_goal_minutes INTEGER NOT NULL DEFAULT 180,
      weekly_focus_goal_minutes INTEGER NOT NULL DEFAULT 900,
      enable_goal_reminders INTEGER NOT NULL DEFAULT 1,
      created_at INTEGER NOT NULL,
      updated_at INTEGER NOT NULL
    )
  ''';

  // 统计缓存表（用于优化查询性能）
  static const String statisticsCache = 'statistics_cache';
  static const String createStatisticsCacheTable = '''
    CREATE TABLE $statisticsCache (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      cache_key TEXT UNIQUE NOT NULL,
      cache_value TEXT NOT NULL,
      expires_at INTEGER NOT NULL,
      created_at INTEGER NOT NULL
    )
  ''';

  // 索引定义
  static const List<String> indexes = [
    'CREATE INDEX idx_focus_sessions_start_time ON $focusSessions (start_time)',
    'CREATE INDEX idx_focus_sessions_is_completed ON $focusSessions (is_completed)',
    'CREATE INDEX idx_break_records_session_id ON $breakRecords (session_id)',
    'CREATE INDEX idx_break_records_start_time ON $breakRecords (start_time)',
    'CREATE INDEX idx_achievements_type ON $achievements (type)',
    'CREATE INDEX idx_statistics_cache_key ON $statisticsCache (cache_key)',
    'CREATE INDEX idx_statistics_cache_expires ON $statisticsCache (expires_at)',
  ];

  // 获取所有表创建语句
  static List<String> get createTableStatements => [
    createFocusSessionsTable,
    createBreakRecordsTable,
    createAchievementsTable,
    createAppSettingsTable,
    createStatisticsCacheTable,
  ];

  // 获取所有表名
  static List<String> get allTables => [
    focusSessions,
    breakRecords,
    achievements,
    appSettings,
    statisticsCache,
  ];
}
