import 'package:sqflite/sqflite.dart';
import 'tables.dart';

// 数据库迁移管理
class DatabaseMigrations {
  static const int currentVersion = 1;

  // 执行数据库迁移
  static Future<void> migrate(Database db, int oldVersion, int newVersion) async {
    print('Migrating database from version $oldVersion to $newVersion');

    for (int version = oldVersion + 1; version <= newVersion; version++) {
      await _migrateToVersion(db, version);
    }
  }

  // 迁移到指定版本
  static Future<void> _migrateToVersion(Database db, int version) async {
    print('Migrating to version $version');

    switch (version) {
      case 1:
        await _migrateToV1(db);
        break;
      default:
        throw Exception('Unknown database version: $version');
    }
  }

  // 迁移到版本1（初始版本）
  static Future<void> _migrateToV1(Database db) async {
    print('Creating initial database schema (v1)');

    // 创建所有表
    for (final statement in DatabaseTables.createTableStatements) {
      await db.execute(statement);
    }

    // 创建索引
    for (final index in DatabaseTables.indexes) {
      await db.execute(index);
    }

    // 插入默认设置
    await _insertDefaultSettings(db);

    print('Database schema v1 created successfully');
  }

  // 插入默认设置
  static Future<void> _insertDefaultSettings(Database db) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    
    await db.insert(DatabaseTables.appSettings, {
      'id': 1,
      'focus_duration_minutes': 90,
      'use_random_break_interval': 1,
      'fixed_break_interval_minutes': 4,
      'min_break_interval_minutes': 3,
      'max_break_interval_minutes': 5,
      'use_random_break_duration': 1,
      'fixed_break_duration_seconds': 8,
      'min_break_duration_seconds': 8,
      'max_break_duration_seconds': 10,
      'audio_theme': 'default',
      'sound_volume': 0.7,
      'enable_vibration': 1,
      'enable_dark_mode': 0,
      'language': 'zh_CN',
      'show_seconds': 1,
      'enable_notifications': 1,
      'daily_focus_goal_minutes': 180,
      'weekly_focus_goal_minutes': 900,
      'enable_goal_reminders': 1,
      'created_at': now,
      'updated_at': now,
    });

    print('Default settings inserted');
  }

  // 清理数据库（用于测试或重置）
  static Future<void> dropAllTables(Database db) async {
    print('Dropping all tables');

    for (final table in DatabaseTables.allTables) {
      await db.execute('DROP TABLE IF EXISTS $table');
    }

    print('All tables dropped');
  }

  // 检查数据库完整性
  static Future<bool> checkIntegrity(Database db) async {
    try {
      final result = await db.rawQuery('PRAGMA integrity_check');
      final isOk = result.isNotEmpty && result.first['integrity_check'] == 'ok';
      
      if (isOk) {
        print('Database integrity check passed');
      } else {
        print('Database integrity check failed: $result');
      }
      
      return isOk;
    } catch (e) {
      print('Database integrity check error: $e');
      return false;
    }
  }

  // 获取数据库信息
  static Future<Map<String, dynamic>> getDatabaseInfo(Database db) async {
    final version = await db.getVersion();
    final tables = await db.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
    );
    
    final info = <String, dynamic>{
      'version': version,
      'tables': tables.map((t) => t['name']).toList(),
      'path': db.path,
    };

    // 获取每个表的行数
    for (final table in DatabaseTables.allTables) {
      try {
        final count = await db.rawQuery('SELECT COUNT(*) as count FROM $table');
        info['${table}_count'] = count.first['count'];
      } catch (e) {
        info['${table}_count'] = 'error: $e';
      }
    }

    return info;
  }
}
