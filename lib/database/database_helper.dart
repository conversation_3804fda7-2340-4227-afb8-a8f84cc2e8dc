import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'migrations.dart';
import 'tables.dart';

// 数据库管理类
class DatabaseHelper {
  static DatabaseHelper? _instance;
  static Database? _database;
  static final Completer<void> _initCompleter = Completer<void>();
  static bool _isInitializing = false;

  // 单例模式
  DatabaseHelper._internal();

  static DatabaseHelper get instance {
    _instance ??= DatabaseHelper._internal();
    return _instance!;
  }

  // 获取数据库实例
  Future<Database> get database async {
    if (_database != null) return _database!;
    
    // 等待初始化完成
    if (_isInitializing) {
      await _initCompleter.future;
      return _database!;
    }

    _database = await _initDatabase();
    return _database!;
  }

  // 初始化数据库
  Future<Database> _initDatabase() async {
    _isInitializing = true;
    
    try {
      print('DatabaseHelper: Initializing database');
      
      // 获取数据库路径
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, 'interflow.db');
      
      print('DatabaseHelper: Database path: $path');

      // 打开数据库
      final database = await openDatabase(
        path,
        version: DatabaseMigrations.currentVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onDowngrade: _onDowngrade,
        onOpen: _onOpen,
      );

      print('DatabaseHelper: Database initialized successfully');
      
      if (!_initCompleter.isCompleted) {
        _initCompleter.complete();
      }
      
      return database;
    } catch (e) {
      print('DatabaseHelper: Failed to initialize database: $e');
      _isInitializing = false;
      
      if (!_initCompleter.isCompleted) {
        _initCompleter.completeError(e);
      }
      
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }

  // 数据库创建回调
  Future<void> _onCreate(Database db, int version) async {
    print('DatabaseHelper: Creating database version $version');
    await DatabaseMigrations.migrate(db, 0, version);
  }

  // 数据库升级回调
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    print('DatabaseHelper: Upgrading database from $oldVersion to $newVersion');
    await DatabaseMigrations.migrate(db, oldVersion, newVersion);
  }

  // 数据库降级回调
  Future<void> _onDowngrade(Database db, int oldVersion, int newVersion) async {
    print('DatabaseHelper: Downgrading database from $oldVersion to $newVersion');
    // 通常不建议降级，这里可以抛出异常或重新创建数据库
    throw Exception('Database downgrade not supported');
  }

  // 数据库打开回调
  Future<void> _onOpen(Database db) async {
    print('DatabaseHelper: Database opened');
    
    // 启用外键约束
    await db.execute('PRAGMA foreign_keys = ON');
    
    // 检查数据库完整性
    final isIntegrityOk = await DatabaseMigrations.checkIntegrity(db);
    if (!isIntegrityOk) {
      print('DatabaseHelper: Database integrity check failed');
    }
  }

  // 执行事务
  Future<T> transaction<T>(Future<T> Function(Transaction txn) action) async {
    final db = await database;
    return await db.transaction(action);
  }

  // 执行原始查询
  Future<List<Map<String, dynamic>>> rawQuery(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    return await db.rawQuery(sql, arguments);
  }

  // 执行原始SQL
  Future<void> rawExecute(String sql, [List<dynamic>? arguments]) async {
    final db = await database;
    await db.rawQuery(sql, arguments);
  }

  // 插入数据
  Future<int> insert(String table, Map<String, dynamic> values) async {
    final db = await database;
    return await db.insert(table, values);
  }

  // 查询数据
  Future<List<Map<String, dynamic>>> query(
    String table, {
    bool? distinct,
    List<String>? columns,
    String? where,
    List<dynamic>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    return await db.query(
      table,
      distinct: distinct,
      columns: columns,
      where: where,
      whereArgs: whereArgs,
      groupBy: groupBy,
      having: having,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  // 更新数据
  Future<int> update(
    String table,
    Map<String, dynamic> values, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    return await db.update(table, values, where: where, whereArgs: whereArgs);
  }

  // 删除数据
  Future<int> delete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    return await db.delete(table, where: where, whereArgs: whereArgs);
  }

  // 获取数据库信息
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    final db = await database;
    return await DatabaseMigrations.getDatabaseInfo(db);
  }

  // 清理缓存
  Future<void> clearCache() async {
    final db = await database;
    await db.delete(DatabaseTables.statisticsCache);
    print('DatabaseHelper: Cache cleared');
  }

  // 关闭数据库
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      print('DatabaseHelper: Database closed');
    }
  }

  // 重置数据库（用于测试）
  Future<void> reset() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
    
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'interflow.db');
    await deleteDatabase(path);
    
    print('DatabaseHelper: Database reset');
  }
}
