import '../database/database_helper.dart';
import '../database/tables.dart';
import '../models/settings.dart';

// 设置数据访问对象
class SettingsDao {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;

  // 获取应用设置
  Future<AppSettings> getSettings() async {
    final settings = await _dbHelper.query(
      DatabaseTables.appSettings,
      where: 'id = 1',
    );

    if (settings.isEmpty) {
      // 如果没有设置记录，返回默认设置并保存到数据库
      final defaultSettings = AppSettings();
      await saveSettings(defaultSettings);
      return defaultSettings;
    }

    return _mapToAppSettings(settings.first);
  }

  // 保存应用设置
  Future<void> saveSettings(AppSettings settings) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    
    final data = {
      'id': 1,
      'focus_duration_minutes': settings.focusDurationMinutes,
      'use_random_break_interval': settings.useRandomBreakInterval ? 1 : 0,
      'fixed_break_interval_minutes': settings.fixedBreakIntervalMinutes,
      'min_break_interval_minutes': settings.minBreakIntervalMinutes,
      'max_break_interval_minutes': settings.maxBreakIntervalMinutes,
      'use_random_break_duration': settings.useRandomBreakDuration ? 1 : 0,
      'fixed_break_duration_seconds': settings.fixedBreakDurationSeconds,
      'min_break_duration_seconds': settings.minBreakDurationSeconds,
      'max_break_duration_seconds': settings.maxBreakDurationSeconds,
      'audio_theme': settings.audioTheme,
      'sound_volume': settings.soundVolume,
      'enable_vibration': settings.enableVibration ? 1 : 0,
      'enable_dark_mode': settings.enableDarkMode ? 1 : 0,
      'language': settings.language,
      'show_seconds': settings.showSeconds ? 1 : 0,
      'enable_notifications': settings.enableNotifications ? 1 : 0,
      'daily_focus_goal_minutes': settings.dailyFocusGoalMinutes,
      'weekly_focus_goal_minutes': settings.weeklyFocusGoalMinutes,
      'enable_goal_reminders': settings.enableGoalReminders ? 1 : 0,
      'updated_at': now,
    };

    // 检查是否已存在设置记录
    final existing = await _dbHelper.query(
      DatabaseTables.appSettings,
      where: 'id = 1',
    );

    if (existing.isEmpty) {
      // 插入新记录
      data['created_at'] = now;
      await _dbHelper.insert(DatabaseTables.appSettings, data);
    } else {
      // 更新现有记录
      await _dbHelper.update(
        DatabaseTables.appSettings,
        data,
        where: 'id = 1',
      );
    }
  }

  // 更新特定设置项
  Future<void> updateSetting(String key, dynamic value) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    
    await _dbHelper.update(
      DatabaseTables.appSettings,
      {
        key: value,
        'updated_at': now,
      },
      where: 'id = 1',
    );
  }

  // 重置设置为默认值
  Future<void> resetSettings() async {
    await _dbHelper.delete(
      DatabaseTables.appSettings,
      where: 'id = 1',
    );
    
    // 重新保存默认设置
    await saveSettings(AppSettings());
  }

  // 导出设置为JSON
  Future<Map<String, dynamic>> exportSettings() async {
    final settings = await getSettings();
    return settings.toJson();
  }

  // 从JSON导入设置
  Future<void> importSettings(Map<String, dynamic> json) async {
    final settings = AppSettings.fromJson(json);
    await saveSettings(settings);
  }

  // 获取设置的最后更新时间
  Future<DateTime?> getLastUpdateTime() async {
    final settings = await _dbHelper.query(
      DatabaseTables.appSettings,
      columns: ['updated_at'],
      where: 'id = 1',
    );

    if (settings.isEmpty) return null;

    return DateTime.fromMillisecondsSinceEpoch(settings.first['updated_at'] as int);
  }

  // 检查设置是否存在
  Future<bool> settingsExist() async {
    final settings = await _dbHelper.query(
      DatabaseTables.appSettings,
      columns: ['id'],
      where: 'id = 1',
    );

    return settings.isNotEmpty;
  }

  // 将数据库记录映射为AppSettings对象
  AppSettings _mapToAppSettings(Map<String, dynamic> data) {
    return AppSettings(
      focusDurationMinutes: data['focus_duration_minutes'] as int,
      useRandomBreakInterval: (data['use_random_break_interval'] as int) == 1,
      fixedBreakIntervalMinutes: data['fixed_break_interval_minutes'] as int,
      minBreakIntervalMinutes: data['min_break_interval_minutes'] as int,
      maxBreakIntervalMinutes: data['max_break_interval_minutes'] as int,
      useRandomBreakDuration: (data['use_random_break_duration'] as int) == 1,
      fixedBreakDurationSeconds: data['fixed_break_duration_seconds'] as int,
      minBreakDurationSeconds: data['min_break_duration_seconds'] as int,
      maxBreakDurationSeconds: data['max_break_duration_seconds'] as int,
      audioTheme: data['audio_theme'] as String,
      soundVolume: (data['sound_volume'] as num).toDouble(),
      enableVibration: (data['enable_vibration'] as int) == 1,
      enableDarkMode: (data['enable_dark_mode'] as int) == 1,
      language: data['language'] as String,
      showSeconds: (data['show_seconds'] as int) == 1,
      enableNotifications: (data['enable_notifications'] as int) == 1,
      dailyFocusGoalMinutes: data['daily_focus_goal_minutes'] as int,
      weeklyFocusGoalMinutes: data['weekly_focus_goal_minutes'] as int,
      enableGoalReminders: (data['enable_goal_reminders'] as int) == 1,
    );
  }
}
