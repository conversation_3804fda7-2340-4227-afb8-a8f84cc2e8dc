import '../database/database_helper.dart';
import '../database/tables.dart';
import '../models/statistics.dart';
import '../utils/constants.dart';

// 成就数据访问对象
class AchievementDao {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;

  // 插入成就
  Future<void> insertAchievement(Achievement achievement) async {
    final now = DateTime.now().millisecondsSinceEpoch;

    await _dbHelper.insert(DatabaseTables.achievements, {
      'id': achievement.id,
      'name': achievement.name,
      'description': achievement.description,
      'type': achievement.type.index,
      'unlocked_at': achievement.unlockedAt.millisecondsSinceEpoch,
      'created_at': now,
    });
  }

  // 批量插入成就
  Future<void> insertAchievements(List<Achievement> achievements) async {
    await _dbHelper.transaction((txn) async {
      final now = DateTime.now().millisecondsSinceEpoch;

      for (final achievement in achievements) {
        await txn.insert(DatabaseTables.achievements, {
          'id': achievement.id,
          'name': achievement.name,
          'description': achievement.description,
          'type': achievement.type.index,
          'unlocked_at': achievement.unlockedAt.millisecondsSinceEpoch,
          'created_at': now,
        });
      }
    });
  }

  // 根据ID获取成就
  Future<Achievement?> getAchievementById(String id) async {
    final achievements = await _dbHelper.query(
      DatabaseTables.achievements,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (achievements.isEmpty) return null;

    return _mapToAchievement(achievements.first);
  }

  // 获取所有成就
  Future<List<Achievement>> getAllAchievements({
    String? orderBy,
  }) async {
    final achievements = await _dbHelper.query(
      DatabaseTables.achievements,
      orderBy: orderBy ?? 'unlocked_at DESC',
    );

    return achievements.map(_mapToAchievement).toList();
  }

  // 根据类型获取成就
  Future<List<Achievement>> getAchievementsByType(AchievementType type) async {
    final achievements = await _dbHelper.query(
      DatabaseTables.achievements,
      where: 'type = ?',
      whereArgs: [type.index],
      orderBy: 'unlocked_at DESC',
    );

    return achievements.map(_mapToAchievement).toList();
  }

  // 获取最近解锁的成就
  Future<List<Achievement>> getRecentAchievements({int limit = 5}) async {
    final achievements = await _dbHelper.query(
      DatabaseTables.achievements,
      orderBy: 'unlocked_at DESC',
      limit: limit,
    );

    return achievements.map(_mapToAchievement).toList();
  }

  // 检查成就是否存在
  Future<bool> achievementExists(String id) async {
    final achievements = await _dbHelper.query(
      DatabaseTables.achievements,
      columns: ['id'],
      where: 'id = ?',
      whereArgs: [id],
    );

    return achievements.isNotEmpty;
  }

  // 删除成就
  Future<void> deleteAchievement(String id) async {
    await _dbHelper.delete(
      DatabaseTables.achievements,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // 删除所有成就
  Future<void> deleteAllAchievements() async {
    await _dbHelper.delete(DatabaseTables.achievements);
  }

  // 获取成就统计
  Future<Map<String, dynamic>> getAchievementStatistics() async {
    final totalCount = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseTables.achievements}'
    );

    final typeStats = await _dbHelper.rawQuery('''
      SELECT type, COUNT(*) as count
      FROM ${DatabaseTables.achievements}
      GROUP BY type
    ''');

    final recentCount = await _dbHelper.rawQuery('''
      SELECT COUNT(*) as count
      FROM ${DatabaseTables.achievements}
      WHERE unlocked_at >= ?
    ''', [DateTime.now().subtract(const Duration(days: 7)).millisecondsSinceEpoch]);

    return {
      'total_count': totalCount.first['count'],
      'type_stats': typeStats,
      'recent_count': recentCount.first['count'],
    };
  }

  // 获取成就进度（用于显示未解锁成就的进度）
  Future<Map<String, double>> getAchievementProgress() async {
    // 这里可以根据实际需求计算各种成就的进度
    // 例如：距离下一个成就还需要多少次专注、多少天连续等

    final progress = <String, double>{};

    // 示例：计算连续天数成就的进度
    // 这里需要根据实际的专注数据来计算

    return progress;
  }

  // 将数据库记录映射为Achievement对象
  Achievement _mapToAchievement(Map<String, dynamic> data) {
    return Achievement(
      id: data['id'] as String,
      name: data['name'] as String,
      description: data['description'] as String,
      type: AchievementType.values[data['type'] as int],
      unlockedAt: DateTime.fromMillisecondsSinceEpoch(data['unlocked_at'] as int),
    );
  }

  // 获取成就映射（用于兼容现有代码）
  Future<Map<String, Achievement>> getAchievementMap() async {
    final achievements = await getAllAchievements();
    final map = <String, Achievement>{};

    for (final achievement in achievements) {
      map[achievement.id] = achievement;
    }

    return map;
  }
}
