import '../database/database_helper.dart';
import '../database/tables.dart';
import '../models/focus_session.dart';
import '../utils/constants.dart';

// 专注会话数据访问对象
class FocusSessionDao {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;

  // 插入专注会话
  Future<void> insertSession(FocusSession session) async {
    print('FocusSessionDao.insertSession: Starting to insert session ${session.id}');
    print('FocusSessionDao.insertSession: Session details - duration: ${session.durationMinutes}min, elapsed: ${session.elapsedMinutes}:${session.elapsedSeconds}, completed: ${session.isCompleted}');

    await _dbHelper.transaction((txn) async {
      final now = DateTime.now().millisecondsSinceEpoch;

      // 插入会话记录
      final sessionData = {
        'id': session.id,
        'start_time': session.startTime.millisecondsSinceEpoch,
        'end_time': session.endTime?.millisecondsSinceEpoch,
        'duration_minutes': session.durationMinutes,
        'state': session.state.index,
        'elapsed_minutes': session.elapsedMinutes,
        'elapsed_seconds': session.elapsedSeconds,
        'is_completed': session.isCompleted ? 1 : 0,
        'created_at': now,
        'updated_at': now,
      };

      print('FocusSessionDao.insertSession: Inserting session data: $sessionData');
      await txn.insert(DatabaseTables.focusSessions, sessionData);

      // 插入休息记录
      print('FocusSessionDao.insertSession: Inserting ${session.breaks.length} break records');
      for (final breakRecord in session.breaks) {
        final breakData = {
          'session_id': session.id,
          'start_time': breakRecord.startTime.millisecondsSinceEpoch,
          'duration_seconds': breakRecord.durationSeconds,
          'reaction_time_seconds': breakRecord.reactionTimeSeconds,
          'created_at': now,
        };
        print('FocusSessionDao.insertSession: Inserting break record: $breakData');
        await txn.insert(DatabaseTables.breakRecords, breakData);
      }
    });

    print('FocusSessionDao.insertSession: Successfully inserted session ${session.id} with ${session.breaks.length} breaks');
  }

  // 更新专注会话
  Future<void> updateSession(FocusSession session) async {
    print('FocusSessionDao.updateSession: Starting to update session ${session.id}');
    print('FocusSessionDao.updateSession: Session details - elapsed: ${session.elapsedMinutes}:${session.elapsedSeconds}, state: ${session.state}, completed: ${session.isCompleted}');

    await _dbHelper.transaction((txn) async {
      final now = DateTime.now().millisecondsSinceEpoch;

      // 更新会话记录
      final updateData = {
        'end_time': session.endTime?.millisecondsSinceEpoch,
        'state': session.state.index,
        'elapsed_minutes': session.elapsedMinutes,
        'elapsed_seconds': session.elapsedSeconds,
        'is_completed': session.isCompleted ? 1 : 0,
        'updated_at': now,
      };

      print('FocusSessionDao.updateSession: Updating session with data: $updateData');
      final updatedRows = await txn.update(
        DatabaseTables.focusSessions,
        updateData,
        where: 'id = ?',
        whereArgs: [session.id],
      );

      print('FocusSessionDao.updateSession: Updated $updatedRows rows for session ${session.id}');

      // 删除旧的休息记录
      final deletedBreaks = await txn.delete(
        DatabaseTables.breakRecords,
        where: 'session_id = ?',
        whereArgs: [session.id],
      );
      print('FocusSessionDao.updateSession: Deleted $deletedBreaks old break records');

      // 插入新的休息记录
      print('FocusSessionDao.updateSession: Inserting ${session.breaks.length} new break records');
      for (final breakRecord in session.breaks) {
        final breakData = {
          'session_id': session.id,
          'start_time': breakRecord.startTime.millisecondsSinceEpoch,
          'duration_seconds': breakRecord.durationSeconds,
          'reaction_time_seconds': breakRecord.reactionTimeSeconds,
          'created_at': now,
        };
        print('FocusSessionDao.updateSession: Inserting break record: $breakData');
        await txn.insert(DatabaseTables.breakRecords, breakData);
      }
    });

    print('FocusSessionDao.updateSession: Successfully updated session ${session.id} with ${session.breaks.length} breaks');
  }

  // 根据ID获取专注会话
  Future<FocusSession?> getSessionById(String id) async {
    final sessions = await _dbHelper.query(
      DatabaseTables.focusSessions,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (sessions.isEmpty) return null;

    final sessionData = sessions.first;
    final breaks = await _getBreakRecords(id);

    return _mapToFocusSession(sessionData, breaks);
  }

  // 获取所有专注会话
  Future<List<FocusSession>> getAllSessions({
    int? limit,
    int? offset,
    String? orderBy,
  }) async {
    print('FocusSessionDao.getAllSessions: Querying all sessions with limit=$limit, offset=$offset, orderBy=$orderBy');

    final sessions = await _dbHelper.query(
      DatabaseTables.focusSessions,
      orderBy: orderBy ?? 'start_time DESC',
      limit: limit,
      offset: offset,
    );

    print('FocusSessionDao.getAllSessions: Found ${sessions.length} sessions in database');

    final result = <FocusSession>[];
    for (final sessionData in sessions) {
      final breaks = await _getBreakRecords(sessionData['id'] as String);
      result.add(_mapToFocusSession(sessionData, breaks));
    }

    print('FocusSessionDao.getAllSessions: Returning ${result.length} sessions with breaks loaded');
    return result;
  }

  // 获取指定日期范围的会话
  Future<List<FocusSession>> getSessionsByDateRange(
    DateTime startDate,
    DateTime endDate, {
    bool? completedOnly,
  }) async {
    print('FocusSessionDao.getSessionsByDateRange: Querying sessions from ${startDate.toIso8601String()} to ${endDate.toIso8601String()}, completedOnly=$completedOnly');

    String where = 'start_time >= ? AND start_time <= ?';
    List<dynamic> whereArgs = [
      startDate.millisecondsSinceEpoch,
      endDate.millisecondsSinceEpoch,
    ];

    if (completedOnly == true) {
      where += ' AND is_completed = 1';
    }

    final sessions = await _dbHelper.query(
      DatabaseTables.focusSessions,
      where: where,
      whereArgs: whereArgs,
      orderBy: 'start_time DESC',
    );

    print('FocusSessionDao.getSessionsByDateRange: Found ${sessions.length} sessions in date range');

    final result = <FocusSession>[];
    for (final sessionData in sessions) {
      final breaks = await _getBreakRecords(sessionData['id'] as String);
      final session = _mapToFocusSession(sessionData, breaks);
      print('FocusSessionDao.getSessionsByDateRange: Session ${session.id} - elapsed: ${session.elapsedMinutes}min, completed: ${session.isCompleted}');
      result.add(session);
    }

    print('FocusSessionDao.getSessionsByDateRange: Returning ${result.length} sessions');
    return result;
  }

  // 获取今日会话
  Future<List<FocusSession>> getTodaySessions() async {
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return await getSessionsByDateRange(startOfDay, endOfDay);
  }

  // 获取本周会话
  Future<List<FocusSession>> getWeekSessions() async {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final startOfWeekDay = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);
    final endOfWeek = startOfWeekDay.add(const Duration(days: 7));

    return await getSessionsByDateRange(startOfWeekDay, endOfWeek);
  }

  // 获取本月会话
  Future<List<FocusSession>> getMonthSessions() async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 1);

    return await getSessionsByDateRange(startOfMonth, endOfMonth);
  }

  // 删除专注会话
  Future<void> deleteSession(String id) async {
    await _dbHelper.transaction((txn) async {
      // 删除休息记录（外键约束会自动删除）
      await txn.delete(
        DatabaseTables.breakRecords,
        where: 'session_id = ?',
        whereArgs: [id],
      );

      // 删除会话记录
      await txn.delete(
        DatabaseTables.focusSessions,
        where: 'id = ?',
        whereArgs: [id],
      );
    });
  }

  // 获取会话的休息记录
  Future<List<BreakRecord>> _getBreakRecords(String sessionId) async {
    final breaks = await _dbHelper.query(
      DatabaseTables.breakRecords,
      where: 'session_id = ?',
      whereArgs: [sessionId],
      orderBy: 'start_time ASC',
    );

    return breaks.map((breakData) => BreakRecord(
      startTime: DateTime.fromMillisecondsSinceEpoch(breakData['start_time'] as int),
      durationSeconds: breakData['duration_seconds'] as int,
      reactionTimeSeconds: (breakData['reaction_time_seconds'] as num).toDouble(),
    )).toList();
  }

  // 将数据库记录映射为FocusSession对象
  FocusSession _mapToFocusSession(
    Map<String, dynamic> sessionData,
    List<BreakRecord> breaks,
  ) {
    return FocusSession(
      id: sessionData['id'] as String,
      startTime: DateTime.fromMillisecondsSinceEpoch(sessionData['start_time'] as int),
      endTime: sessionData['end_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch(sessionData['end_time'] as int)
          : null,
      durationMinutes: sessionData['duration_minutes'] as int,
      state: FocusSessionState.values[sessionData['state'] as int],
      elapsedMinutes: sessionData['elapsed_minutes'] as int,
      elapsedSeconds: sessionData['elapsed_seconds'] as int,
      breaks: breaks,
      isCompleted: (sessionData['is_completed'] as int) == 1,
    );
  }

  // 获取统计数据
  Future<Map<String, dynamic>> getStatistics() async {
    final totalSessions = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseTables.focusSessions}'
    );

    final completedSessions = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseTables.focusSessions} WHERE is_completed = 1'
    );

    final totalBreaks = await _dbHelper.rawQuery(
      'SELECT COUNT(*) as count FROM ${DatabaseTables.breakRecords}'
    );

    return {
      'total_sessions': totalSessions.first['count'],
      'completed_sessions': completedSessions.first['count'],
      'total_breaks': totalBreaks.first['count'],
    };
  }
}
